<template>
  <div class="sidebar">
    <!-- Logo区域 -->
    <div class="logo-section">
      <div class="logo">
        <div class="logo-icon">C</div>
        <span class="logo-text">ClarityUI</span>
      </div>
    </div>

    <!-- 导航菜单 -->
    <nav class="nav-menu">
      <router-link 
        v-for="item in menuItems" 
        :key="item.path"
        :to="item.path"
        class="nav-item"
        :class="{ 'has-children': item.children }"
      >
        <div class="nav-item-content">
          <i :class="item.icon" class="nav-icon"></i>
          <span class="nav-text">{{ item.name }}</span>
          <i v-if="item.children" class="chevron-icon">▼</i>
        </div>
      </router-link>
    </nav>

    <!-- 用户信息 -->
    <div class="user-section">
      <div class="user-info">
        <div class="user-avatar">
          <img src="/api/placeholder/32/32" alt="User Avatar" />
        </div>
        <div class="user-details">
          <div class="user-name"><PERSON><PERSON></div>
        </div>
        <i class="settings-icon">⚙</i>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface MenuItem {
  name: string
  path: string
  icon: string
  children?: MenuItem[]
}

const menuItems = ref<MenuItem[]>([
  {
    name: 'Dashboard',
    path: '/dashboard',
    icon: '📊'
  },
  {
    name: 'Tickets',
    path: '/tickets',
    icon: '🎫'
  },
  {
    name: 'Agents',
    path: '/agents',
    icon: '👥'
  },
  {
    name: 'Customers',
    path: '/customers',
    icon: '👤'
  },
  {
    name: 'Products',
    path: '/products',
    icon: '📦'
  },
  {
    name: 'Orders',
    path: '/orders',
    icon: '📋'
  },
  {
    name: 'Analytics',
    path: '/analytics',
    icon: '📈'
  },
  {
    name: 'Settings',
    path: '/settings',
    icon: '⚙️'
  }
])
</script>

<style scoped>
.sidebar {
  width: 280px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
}

.logo-section {
  padding: 24px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.logo-text {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.nav-menu {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.nav-item {
  display: block;
  text-decoration: none;
  color: rgba(255, 255, 255, 0.8);
  margin: 4px 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-item.router-link-active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.nav-item-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 12px;
}

.nav-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.nav-text {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
}

.chevron-icon {
  font-size: 10px;
  opacity: 0.6;
}

.user-section {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
}

.settings-icon {
  font-size: 16px;
  opacity: 0.7;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.settings-icon:hover {
  opacity: 1;
}
</style>
