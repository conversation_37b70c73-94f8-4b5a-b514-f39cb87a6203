<template>
  <div class="app-layout">
    <!-- 侧边栏 -->
    <Sidebar />
    
    <!-- 主内容区域 -->
    <div class="main-content">
      <router-view />
    </div>
  </div>
</template>

<script setup lang="ts">
import Sidebar from './Sidebar.vue'
</script>

<style scoped>
.app-layout {
  display: flex;
  height: 100vh;
  background-color: #1a1a1a;
}

.main-content {
  flex: 1;
  background-color: #2d2d2d;
  overflow-y: auto;
}
</style>
