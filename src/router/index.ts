import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 导入页面组件
import Dashboard from '../views/Dashboard.vue'
import Tickets from '../views/Tickets.vue'
import Agents from '../views/Agents.vue'
import Customers from '../views/Customers.vue'
import Products from '../views/Products.vue'
import Orders from '../views/Orders.vue'
import Analytics from '../views/Analytics.vue'
import Settings from '../views/Settings.vue'

// Settings子页面
import Profile from '../views/settings/Profile.vue'
import Password from '../views/settings/Password.vue'
import Team from '../views/settings/Team.vue'
import Notification from '../views/settings/Notification.vue'
import Integrations from '../views/settings/Integrations.vue'
import Licenses from '../views/settings/Licenses.vue'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard
  },
  {
    path: '/tickets',
    name: 'Tickets',
    component: Tickets
  },
  {
    path: '/agents',
    name: 'Agents',
    component: Agents
  },
  {
    path: '/customers',
    name: 'Customers',
    component: Customers
  },
  {
    path: '/products',
    name: 'Products',
    component: Products
  },
  {
    path: '/orders',
    name: 'Orders',
    component: Orders
  },
  {
    path: '/analytics',
    name: 'Analytics',
    component: Analytics
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    redirect: '/settings/profile',
    children: [
      {
        path: 'profile',
        name: 'Profile',
        component: Profile
      },
      {
        path: 'password',
        name: 'Password',
        component: Password
      },
      {
        path: 'team',
        name: 'Team',
        component: Team
      },
      {
        path: 'notification',
        name: 'Notification',
        component: Notification
      },
      {
        path: 'integrations',
        name: 'Integrations',
        component: Integrations
      },
      {
        path: 'licenses',
        name: 'Licenses',
        component: Licenses
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
