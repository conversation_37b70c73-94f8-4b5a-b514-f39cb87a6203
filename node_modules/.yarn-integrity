{"systemParams": "darwin-arm64-93", "modulesFolders": ["node_modules"], "flags": [], "linkedModules": [], "topLevelPatterns": ["@types/node@^20.10.0", "@vitejs/plugin-vue@^5.0.0", "concurrently@^8.2.0", "cross-env@^7.0.3", "electron-builder@^24.9.0", "electron@^28.0.0", "typescript@^5.3.0", "vite-plugin-electron-renderer@^0.14.0", "vite-plugin-electron@^0.28.0", "vite@^5.0.0", "vue-router@4", "vue-tsc@^1.8.0", "vue@^3.4.0", "wait-on@^7.2.0"], "lockfileEntries": {"7zip-bin@~5.2.0": "https://registry.npmmirror.com/7zip-bin/-/7zip-bin-5.2.0.tgz", "@babel/helper-string-parser@^7.27.1": "https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "@babel/helper-validator-identifier@^7.27.1": "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "@babel/parser@^7.27.5": "https://registry.npmmirror.com/@babel/parser/-/parser-7.28.0.tgz", "@babel/runtime@^7.21.0": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/types@^7.28.0": "https://registry.npmmirror.com/@babel/types/-/types-7.28.0.tgz", "@develar/schema-utils@~2.6.5": "https://registry.npmmirror.com/@develar/schema-utils/-/schema-utils-2.6.5.tgz", "@electron/asar@^3.2.1": "https://registry.npmmirror.com/@electron/asar/-/asar-3.4.1.tgz", "@electron/get@^2.0.0": "https://registry.npmmirror.com/@electron/get/-/get-2.0.3.tgz", "@electron/notarize@2.2.1": "https://registry.npmmirror.com/@electron/notarize/-/notarize-2.2.1.tgz", "@electron/osx-sign@1.0.5": "https://registry.npmmirror.com/@electron/osx-sign/-/osx-sign-1.0.5.tgz", "@electron/universal@1.5.1": "https://registry.npmmirror.com/@electron/universal/-/universal-1.5.1.tgz", "@esbuild/aix-ppc64@0.21.5": "https://registry.npmmirror.com/@esbuild/aix-ppc64/-/aix-ppc64-0.21.5.tgz#c7184a326533fcdf1b8ee0733e21c713b975575f", "@esbuild/android-arm64@0.21.5": "https://registry.npmmirror.com/@esbuild/android-arm64/-/android-arm64-0.21.5.tgz#09d9b4357780da9ea3a7dfb833a1f1ff439b4052", "@esbuild/android-arm@0.21.5": "https://registry.npmmirror.com/@esbuild/android-arm/-/android-arm-0.21.5.tgz#9b04384fb771926dfa6d7ad04324ecb2ab9b2e28", "@esbuild/android-x64@0.21.5": "https://registry.npmmirror.com/@esbuild/android-x64/-/android-x64-0.21.5.tgz#29918ec2db754cedcb6c1b04de8cd6547af6461e", "@esbuild/darwin-arm64@0.21.5": "https://registry.npmmirror.com/@esbuild/darwin-arm64/-/darwin-arm64-0.21.5.tgz", "@esbuild/darwin-x64@0.21.5": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.21.5.tgz#c13838fa57372839abdddc91d71542ceea2e1e22", "@esbuild/freebsd-arm64@0.21.5": "https://registry.npmmirror.com/@esbuild/freebsd-arm64/-/freebsd-arm64-0.21.5.tgz#646b989aa20bf89fd071dd5dbfad69a3542e550e", "@esbuild/freebsd-x64@0.21.5": "https://registry.npmmirror.com/@esbuild/freebsd-x64/-/freebsd-x64-0.21.5.tgz#aa615cfc80af954d3458906e38ca22c18cf5c261", "@esbuild/linux-arm64@0.21.5": "https://registry.npmmirror.com/@esbuild/linux-arm64/-/linux-arm64-0.21.5.tgz#70ac6fa14f5cb7e1f7f887bcffb680ad09922b5b", "@esbuild/linux-arm@0.21.5": "https://registry.npmmirror.com/@esbuild/linux-arm/-/linux-arm-0.21.5.tgz#fc6fd11a8aca56c1f6f3894f2bea0479f8f626b9", "@esbuild/linux-ia32@0.21.5": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.21.5.tgz#3271f53b3f93e3d093d518d1649d6d68d346ede2", "@esbuild/linux-loong64@0.21.5": "https://registry.npmmirror.com/@esbuild/linux-loong64/-/linux-loong64-0.21.5.tgz#ed62e04238c57026aea831c5a130b73c0f9f26df", "@esbuild/linux-mips64el@0.21.5": "https://registry.npmmirror.com/@esbuild/linux-mips64el/-/linux-mips64el-0.21.5.tgz#e79b8eb48bf3b106fadec1ac8240fb97b4e64cbe", "@esbuild/linux-ppc64@0.21.5": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.21.5.tgz#5f2203860a143b9919d383ef7573521fb154c3e4", "@esbuild/linux-riscv64@0.21.5": "https://registry.npmmirror.com/@esbuild/linux-riscv64/-/linux-riscv64-0.21.5.tgz#07bcafd99322d5af62f618cb9e6a9b7f4bb825dc", "@esbuild/linux-s390x@0.21.5": "https://registry.npmmirror.com/@esbuild/linux-s390x/-/linux-s390x-0.21.5.tgz#b7ccf686751d6a3e44b8627ababc8be3ef62d8de", "@esbuild/linux-x64@0.21.5": "https://registry.npmmirror.com/@esbuild/linux-x64/-/linux-x64-0.21.5.tgz#6d8f0c768e070e64309af8004bb94e68ab2bb3b0", "@esbuild/netbsd-x64@0.21.5": "https://registry.npmmirror.com/@esbuild/netbsd-x64/-/netbsd-x64-0.21.5.tgz#bbe430f60d378ecb88decb219c602667387a6047", "@esbuild/openbsd-x64@0.21.5": "https://registry.npmmirror.com/@esbuild/openbsd-x64/-/openbsd-x64-0.21.5.tgz#99d1cf2937279560d2104821f5ccce220cb2af70", "@esbuild/sunos-x64@0.21.5": "https://registry.npmmirror.com/@esbuild/sunos-x64/-/sunos-x64-0.21.5.tgz#08741512c10d529566baba837b4fe052c8f3487b", "@esbuild/win32-arm64@0.21.5": "https://registry.npmmirror.com/@esbuild/win32-arm64/-/win32-arm64-0.21.5.tgz#675b7385398411240735016144ab2e99a60fc75d", "@esbuild/win32-ia32@0.21.5": "https://registry.npmmirror.com/@esbuild/win32-ia32/-/win32-ia32-0.21.5.tgz#1bfc3ce98aa6ca9a0969e4d2af72144c59c1193b", "@esbuild/win32-x64@0.21.5": "https://registry.npmmirror.com/@esbuild/win32-x64/-/win32-x64-0.21.5.tgz#acad351d582d157bb145535db2a6ff53dd514b5c", "@hapi/hoek@^9.0.0": "https://registry.npmmirror.com/@hapi/hoek/-/hoek-9.3.0.tgz", "@hapi/hoek@^9.3.0": "https://registry.npmmirror.com/@hapi/hoek/-/hoek-9.3.0.tgz", "@hapi/topo@^5.1.0": "https://registry.npmmirror.com/@hapi/topo/-/topo-5.1.0.tgz", "@isaacs/cliui@^8.0.2": "https://registry.npmmirror.com/@isaacs/cliui/-/cliui-8.0.2.tgz", "@jridgewell/sourcemap-codec@^1.5.0": "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "@malept/cross-spawn-promise@^1.1.0": "https://registry.npmmirror.com/@malept/cross-spawn-promise/-/cross-spawn-promise-1.1.1.tgz", "@malept/flatpak-bundler@^0.4.0": "https://registry.npmmirror.com/@malept/flatpak-bundler/-/flatpak-bundler-0.4.0.tgz", "@pkgjs/parseargs@^0.11.0": "https://registry.npmmirror.com/@pkgjs/parseargs/-/parseargs-0.11.0.tgz", "@rollup/rollup-android-arm-eabi@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.44.2.tgz#6819b7f1e41a49af566f629a1556eaeea774d043", "@rollup/rollup-android-arm64@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.44.2.tgz#7bd5591af68c64a75be1779e2b20f187878daba9", "@rollup/rollup-darwin-arm64@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.44.2.tgz", "@rollup/rollup-darwin-x64@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.44.2.tgz#202f80eea3acfe3f67496fedffa006a5f1ce7f5a", "@rollup/rollup-freebsd-arm64@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.44.2.tgz#4880f9769f1a7eec436b9c146e1d714338c26567", "@rollup/rollup-freebsd-x64@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.44.2.tgz#647d6e333349b1c0fb322c2827ba1a53a0f10301", "@rollup/rollup-linux-arm-gnueabihf@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.44.2.tgz#7ba5c97a7224f49618861d093c4a7b40fa50867b", "@rollup/rollup-linux-arm-musleabihf@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.44.2.tgz#f858dcf498299d6c625ec697a5191e0e41423905", "@rollup/rollup-linux-arm64-gnu@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.44.2.tgz#c0f1fc20c50666c61f574536a00cdd486b6aaae1", "@rollup/rollup-linux-arm64-musl@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.44.2.tgz#0214efc3e404ddf108e946ad5f7e4ee2792a155a", "@rollup/rollup-linux-loongarch64-gnu@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.44.2.tgz#8303c4ea2ae7bcbb96b2c77cfb53527d964bfceb", "@rollup/rollup-linux-powerpc64le-gnu@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.44.2.tgz#4197ffbc61809629094c0fccf825e43a40fbc0ca", "@rollup/rollup-linux-riscv64-gnu@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.44.2.tgz#bcb99c9004c9b91e3704a6a70c892cb0599b1f42", "@rollup/rollup-linux-riscv64-musl@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.44.2.tgz#3e943bae9b8b4637c573c1922392beb8a5e81acb", "@rollup/rollup-linux-s390x-gnu@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.44.2.tgz#dc43fb467bff9547f5b9937f38668da07fa8fa9f", "@rollup/rollup-linux-x64-gnu@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.44.2.tgz#0699c560fa6ce6b846581a7e6c30c85c22a3f0da", "@rollup/rollup-linux-x64-musl@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.44.2.tgz#9fb1becedcdc9e227d4748576eb8ba2fad8d2e29", "@rollup/rollup-win32-arm64-msvc@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.44.2.tgz#fcf3e62edd76c560252b819f69627685f65887d7", "@rollup/rollup-win32-ia32-msvc@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.44.2.tgz#45a5304491d6da4666f6159be4f739d4d43a283f", "@rollup/rollup-win32-x64-msvc@4.44.2": "https://registry.npmmirror.com/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.44.2.tgz#660018c9696ad4f48abe8c5d56db53c81aadba25", "@sideway/address@^4.1.5": "https://registry.npmmirror.com/@sideway/address/-/address-4.1.5.tgz", "@sideway/formula@^3.0.1": "https://registry.npmmirror.com/@sideway/formula/-/formula-3.0.1.tgz", "@sideway/pinpoint@^2.0.0": "https://registry.npmmirror.com/@sideway/pinpoint/-/pinpoint-2.0.0.tgz", "@sindresorhus/is@^4.0.0": "https://registry.npmmirror.com/@sindresorhus/is/-/is-4.6.0.tgz", "@szmarczak/http-timer@^4.0.5": "https://registry.npmmirror.com/@szmarczak/http-timer/-/http-timer-4.0.6.tgz", "@tootallnate/once@2": "https://registry.npmmirror.com/@tootallnate/once/-/once-2.0.0.tgz", "@types/cacheable-request@^6.0.1": "https://registry.npmmirror.com/@types/cacheable-request/-/cacheable-request-6.0.3.tgz", "@types/debug@^4.1.6": "https://registry.npmmirror.com/@types/debug/-/debug-4.1.12.tgz", "@types/estree@1.0.8": "https://registry.npmmirror.com/@types/estree/-/estree-1.0.8.tgz", "@types/fs-extra@9.0.13": "https://registry.npmmirror.com/@types/fs-extra/-/fs-extra-9.0.13.tgz", "@types/fs-extra@^9.0.11": "https://registry.npmmirror.com/@types/fs-extra/-/fs-extra-9.0.13.tgz", "@types/http-cache-semantics@*": "https://registry.npmmirror.com/@types/http-cache-semantics/-/http-cache-semantics-4.0.4.tgz", "@types/keyv@^3.1.4": "https://registry.npmmirror.com/@types/keyv/-/keyv-3.1.4.tgz", "@types/ms@*": "https://registry.npmmirror.com/@types/ms/-/ms-2.1.0.tgz", "@types/node@*": "https://registry.npmmirror.com/@types/node/-/node-20.19.7.tgz", "@types/node@^18.11.18": "https://registry.npmmirror.com/@types/node/-/node-18.19.118.tgz", "@types/node@^20.10.0": "https://registry.npmmirror.com/@types/node/-/node-20.19.7.tgz", "@types/plist@^3.0.1": "https://registry.npmmirror.com/@types/plist/-/plist-3.0.5.tgz", "@types/responselike@^1.0.0": "https://registry.npmmirror.com/@types/responselike/-/responselike-1.0.3.tgz", "@types/verror@^1.10.3": "https://registry.npmmirror.com/@types/verror/-/verror-1.10.11.tgz", "@types/yauzl@^2.9.1": "https://registry.npmmirror.com/@types/yauzl/-/yauzl-2.10.3.tgz", "@vitejs/plugin-vue@^5.0.0": "https://registry.npmmirror.com/@vitejs/plugin-vue/-/plugin-vue-5.2.4.tgz", "@volar/language-core@1.11.1": "https://registry.npmmirror.com/@volar/language-core/-/language-core-1.11.1.tgz", "@volar/language-core@~1.11.1": "https://registry.npmmirror.com/@volar/language-core/-/language-core-1.11.1.tgz", "@volar/source-map@1.11.1": "https://registry.npmmirror.com/@volar/source-map/-/source-map-1.11.1.tgz", "@volar/source-map@~1.11.1": "https://registry.npmmirror.com/@volar/source-map/-/source-map-1.11.1.tgz", "@volar/typescript@~1.11.1": "https://registry.npmmirror.com/@volar/typescript/-/typescript-1.11.1.tgz", "@vue/compiler-core@3.5.17": "https://registry.npmmirror.com/@vue/compiler-core/-/compiler-core-3.5.17.tgz", "@vue/compiler-dom@3.5.17": "https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.5.17.tgz", "@vue/compiler-dom@^3.3.0": "https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.5.17.tgz", "@vue/compiler-sfc@3.5.17": "https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.5.17.tgz", "@vue/compiler-ssr@3.5.17": "https://registry.npmmirror.com/@vue/compiler-ssr/-/compiler-ssr-3.5.17.tgz", "@vue/devtools-api@^6.6.4": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.6.4.tgz", "@vue/language-core@1.8.27": "https://registry.npmmirror.com/@vue/language-core/-/language-core-1.8.27.tgz", "@vue/reactivity@3.5.17": "https://registry.npmmirror.com/@vue/reactivity/-/reactivity-3.5.17.tgz", "@vue/runtime-core@3.5.17": "https://registry.npmmirror.com/@vue/runtime-core/-/runtime-core-3.5.17.tgz", "@vue/runtime-dom@3.5.17": "https://registry.npmmirror.com/@vue/runtime-dom/-/runtime-dom-3.5.17.tgz", "@vue/server-renderer@3.5.17": "https://registry.npmmirror.com/@vue/server-renderer/-/server-renderer-3.5.17.tgz", "@vue/shared@3.5.17": "https://registry.npmmirror.com/@vue/shared/-/shared-3.5.17.tgz", "@vue/shared@^3.3.0": "https://registry.npmmirror.com/@vue/shared/-/shared-3.5.17.tgz", "@xmldom/xmldom@^0.8.8": "https://registry.npmmirror.com/@xmldom/xmldom/-/xmldom-0.8.10.tgz", "agent-base@6": "https://registry.npmmirror.com/agent-base/-/agent-base-6.0.2.tgz", "ajv-keywords@^3.4.1": "https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "ajv@^6.10.0": "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz", "ajv@^6.12.0": "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz", "ansi-regex@^5.0.1": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz", "ansi-regex@^6.0.1": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-6.1.0.tgz", "ansi-styles@^4.0.0": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^4.1.0": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^6.1.0": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-6.2.1.tgz", "app-builder-bin@4.0.0": "https://registry.npmmirror.com/app-builder-bin/-/app-builder-bin-4.0.0.tgz", "app-builder-lib@24.13.3": "https://registry.npmmirror.com/app-builder-lib/-/app-builder-lib-24.13.3.tgz", "argparse@^2.0.1": "https://registry.npmmirror.com/argparse/-/argparse-2.0.1.tgz", "assert-plus@^1.0.0": "https://registry.npmmirror.com/assert-plus/-/assert-plus-1.0.0.tgz", "astral-regex@^2.0.0": "https://registry.npmmirror.com/astral-regex/-/astral-regex-2.0.0.tgz", "async-exit-hook@^2.0.1": "https://registry.npmmirror.com/async-exit-hook/-/async-exit-hook-2.0.1.tgz", "async@^3.2.3": "https://registry.npmmirror.com/async/-/async-3.2.6.tgz", "asynckit@^0.4.0": "https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz", "at-least-node@^1.0.0": "https://registry.npmmirror.com/at-least-node/-/at-least-node-1.0.0.tgz", "axios@^1.6.1": "https://registry.npmmirror.com/axios/-/axios-1.10.0.tgz", "balanced-match@^1.0.0": "https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz", "base64-js@^1.3.1": "https://registry.npmmirror.com/base64-js/-/base64-js-1.5.1.tgz", "base64-js@^1.5.1": "https://registry.npmmirror.com/base64-js/-/base64-js-1.5.1.tgz", "bluebird-lst@^1.0.9": "https://registry.npmmirror.com/bluebird-lst/-/bluebird-lst-1.0.9.tgz", "bluebird@^3.5.5": "https://registry.npmmirror.com/bluebird/-/bluebird-3.7.2.tgz", "boolean@^3.0.1": "https://registry.npmmirror.com/boolean/-/boolean-3.2.0.tgz", "brace-expansion@^1.1.7": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.12.tgz", "brace-expansion@^2.0.1": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-2.0.2.tgz", "buffer-crc32@~0.2.3": "https://registry.npmmirror.com/buffer-crc32/-/buffer-crc32-0.2.13.tgz", "buffer-equal@^1.0.0": "https://registry.npmmirror.com/buffer-equal/-/buffer-equal-1.0.1.tgz", "buffer-from@^1.0.0": "https://registry.npmmirror.com/buffer-from/-/buffer-from-1.1.2.tgz", "buffer@^5.1.0": "https://registry.npmmirror.com/buffer/-/buffer-5.7.1.tgz", "builder-util-runtime@9.2.4": "https://registry.npmmirror.com/builder-util-runtime/-/builder-util-runtime-9.2.4.tgz", "builder-util@24.13.1": "https://registry.npmmirror.com/builder-util/-/builder-util-24.13.1.tgz", "cacheable-lookup@^5.0.3": "https://registry.npmmirror.com/cacheable-lookup/-/cacheable-lookup-5.0.4.tgz", "cacheable-request@^7.0.2": "https://registry.npmmirror.com/cacheable-request/-/cacheable-request-7.0.4.tgz", "call-bind-apply-helpers@^1.0.1": "https://registry.npmmirror.com/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "call-bind-apply-helpers@^1.0.2": "https://registry.npmmirror.com/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "chalk@^4.0.2": "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz", "chalk@^4.1.2": "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz", "chownr@^2.0.0": "https://registry.npmmirror.com/chownr/-/chownr-2.0.0.tgz", "chromium-pickle-js@^0.2.0": "https://registry.npmmirror.com/chromium-pickle-js/-/chromium-pickle-js-0.2.0.tgz", "ci-info@^3.2.0": "https://registry.npmmirror.com/ci-info/-/ci-info-3.9.0.tgz", "cli-truncate@^2.1.0": "https://registry.npmmirror.com/cli-truncate/-/cli-truncate-2.1.0.tgz", "cliui@^8.0.1": "https://registry.npmmirror.com/cliui/-/cliui-8.0.1.tgz", "clone-response@^1.0.2": "https://registry.npmmirror.com/clone-response/-/clone-response-1.0.3.tgz", "color-convert@^2.0.1": "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz", "color-name@~1.1.4": "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz", "combined-stream@^1.0.8": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz", "commander@^5.0.0": "https://registry.npmmirror.com/commander/-/commander-5.1.0.tgz", "compare-version@^0.1.2": "https://registry.npmmirror.com/compare-version/-/compare-version-0.1.2.tgz", "computeds@^0.0.1": "https://registry.npmmirror.com/computeds/-/computeds-0.0.1.tgz", "concat-map@0.0.1": "https://registry.npmmirror.com/concat-map/-/concat-map-0.0.1.tgz", "concurrently@^8.2.0": "https://registry.npmmirror.com/concurrently/-/concurrently-8.2.2.tgz", "config-file-ts@^0.2.4": "https://registry.npmmirror.com/config-file-ts/-/config-file-ts-0.2.6.tgz", "core-util-is@1.0.2": "https://registry.npmmirror.com/core-util-is/-/core-util-is-1.0.2.tgz", "crc@^3.8.0": "https://registry.npmmirror.com/crc/-/crc-3.8.0.tgz", "cross-env@^7.0.3": "https://registry.npmmirror.com/cross-env/-/cross-env-7.0.3.tgz", "cross-spawn@^7.0.1": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.6.tgz", "cross-spawn@^7.0.3": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.6.tgz", "cross-spawn@^7.0.6": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.6.tgz", "csstype@^3.1.3": "https://registry.npmmirror.com/csstype/-/csstype-3.1.3.tgz", "date-fns@^2.30.0": "https://registry.npmmirror.com/date-fns/-/date-fns-2.30.0.tgz", "de-indent@^1.0.2": "https://registry.npmmirror.com/de-indent/-/de-indent-1.0.2.tgz", "debug@4": "https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz", "debug@^4.1.0": "https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz", "debug@^4.1.1": "https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz", "debug@^4.3.1": "https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz", "debug@^4.3.4": "https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz", "decompress-response@^6.0.0": "https://registry.npmmirror.com/decompress-response/-/decompress-response-6.0.0.tgz", "defer-to-connect@^2.0.0": "https://registry.npmmirror.com/defer-to-connect/-/defer-to-connect-2.0.1.tgz", "define-data-property@^1.0.1": "https://registry.npmmirror.com/define-data-property/-/define-data-property-1.1.4.tgz", "define-properties@^1.2.1": "https://registry.npmmirror.com/define-properties/-/define-properties-1.2.1.tgz", "delayed-stream@~1.0.0": "https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz", "detect-node@^2.0.4": "https://registry.npmmirror.com/detect-node/-/detect-node-2.1.0.tgz", "dir-compare@^3.0.0": "https://registry.npmmirror.com/dir-compare/-/dir-compare-3.3.0.tgz", "dmg-builder@24.13.3": "https://registry.npmmirror.com/dmg-builder/-/dmg-builder-24.13.3.tgz", "dmg-license@^1.0.11": "https://registry.npmmirror.com/dmg-license/-/dmg-license-1.0.11.tgz", "dotenv-expand@^5.1.0": "https://registry.npmmirror.com/dotenv-expand/-/dotenv-expand-5.1.0.tgz", "dotenv@^9.0.2": "https://registry.npmmirror.com/dotenv/-/dotenv-9.0.2.tgz", "dunder-proto@^1.0.1": "https://registry.npmmirror.com/dunder-proto/-/dunder-proto-1.0.1.tgz", "eastasianwidth@^0.2.0": "https://registry.npmmirror.com/eastasianwidth/-/eastasianwidth-0.2.0.tgz", "ejs@^3.1.8": "https://registry.npmmirror.com/ejs/-/ejs-3.1.10.tgz", "electron-builder@^24.9.0": "https://registry.npmmirror.com/electron-builder/-/electron-builder-24.13.3.tgz", "electron-publish@24.13.1": "https://registry.npmmirror.com/electron-publish/-/electron-publish-24.13.1.tgz", "electron@^28.0.0": "https://registry.npmmirror.com/electron/-/electron-28.3.3.tgz", "emoji-regex@^8.0.0": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz", "emoji-regex@^9.2.2": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-9.2.2.tgz", "end-of-stream@^1.1.0": "https://registry.npmmirror.com/end-of-stream/-/end-of-stream-1.4.4.tgz", "entities@^4.5.0": "https://registry.npmmirror.com/entities/-/entities-4.5.0.tgz", "env-paths@^2.2.0": "https://registry.npmmirror.com/env-paths/-/env-paths-2.2.1.tgz", "err-code@^2.0.2": "https://registry.npmmirror.com/err-code/-/err-code-2.0.3.tgz", "es-define-property@^1.0.0": "https://registry.npmmirror.com/es-define-property/-/es-define-property-1.0.1.tgz", "es-define-property@^1.0.1": "https://registry.npmmirror.com/es-define-property/-/es-define-property-1.0.1.tgz", "es-errors@^1.3.0": "https://registry.npmmirror.com/es-errors/-/es-errors-1.3.0.tgz", "es-object-atoms@^1.0.0": "https://registry.npmmirror.com/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "es-object-atoms@^1.1.1": "https://registry.npmmirror.com/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "es-set-tostringtag@^2.1.0": "https://registry.npmmirror.com/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "es6-error@^4.1.1": "https://registry.npmmirror.com/es6-error/-/es6-error-4.1.1.tgz", "esbuild@^0.21.3": "https://registry.npmmirror.com/esbuild/-/esbuild-0.21.5.tgz", "escalade@^3.1.1": "https://registry.npmmirror.com/escalade/-/escalade-3.2.0.tgz", "escape-string-regexp@^4.0.0": "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "estree-walker@^2.0.2": "https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz", "extract-zip@^2.0.1": "https://registry.npmmirror.com/extract-zip/-/extract-zip-2.0.1.tgz", "extsprintf@^1.2.0": "https://registry.npmmirror.com/extsprintf/-/extsprintf-1.4.1.tgz", "fast-deep-equal@^3.1.1": "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fast-json-stable-stringify@^2.0.0": "https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "fd-slicer@~1.1.0": "https://registry.npmmirror.com/fd-slicer/-/fd-slicer-1.1.0.tgz", "filelist@^1.0.4": "https://registry.npmmirror.com/filelist/-/filelist-1.0.4.tgz", "follow-redirects@^1.15.6": "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.9.tgz", "foreground-child@^3.1.0": "https://registry.npmmirror.com/foreground-child/-/foreground-child-3.3.1.tgz", "form-data@^4.0.0": "https://registry.npmmirror.com/form-data/-/form-data-4.0.3.tgz", "fs-extra@^10.0.0": "https://registry.npmmirror.com/fs-extra/-/fs-extra-10.1.0.tgz", "fs-extra@^10.1.0": "https://registry.npmmirror.com/fs-extra/-/fs-extra-10.1.0.tgz", "fs-extra@^8.1.0": "https://registry.npmmirror.com/fs-extra/-/fs-extra-8.1.0.tgz", "fs-extra@^9.0.0": "https://registry.npmmirror.com/fs-extra/-/fs-extra-9.1.0.tgz", "fs-extra@^9.0.1": "https://registry.npmmirror.com/fs-extra/-/fs-extra-9.1.0.tgz", "fs-minipass@^2.0.0": "https://registry.npmmirror.com/fs-minipass/-/fs-minipass-2.1.0.tgz", "fs.realpath@^1.0.0": "https://registry.npmmirror.com/fs.realpath/-/fs.realpath-1.0.0.tgz", "fsevents@~2.3.2": "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.3.tgz", "fsevents@~2.3.3": "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.3.tgz", "function-bind@^1.1.2": "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.2.tgz", "get-caller-file@^2.0.5": "https://registry.npmmirror.com/get-caller-file/-/get-caller-file-2.0.5.tgz", "get-intrinsic@^1.2.6": "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "get-proto@^1.0.1": "https://registry.npmmirror.com/get-proto/-/get-proto-1.0.1.tgz", "get-stream@^5.1.0": "https://registry.npmmirror.com/get-stream/-/get-stream-5.2.0.tgz", "glob@^10.3.10": "https://registry.npmmirror.com/glob/-/glob-10.4.5.tgz", "glob@^7.1.6": "https://registry.npmmirror.com/glob/-/glob-7.2.3.tgz", "global-agent@^3.0.0": "https://registry.npmmirror.com/global-agent/-/global-agent-3.0.0.tgz", "globalthis@^1.0.1": "https://registry.npmmirror.com/globalthis/-/globalthis-1.0.4.tgz", "gopd@^1.0.1": "https://registry.npmmirror.com/gopd/-/gopd-1.2.0.tgz", "gopd@^1.2.0": "https://registry.npmmirror.com/gopd/-/gopd-1.2.0.tgz", "got@^11.8.5": "https://registry.npmmirror.com/got/-/got-11.8.6.tgz", "graceful-fs@^4.1.6": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.0": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz", "has-flag@^4.0.0": "https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz", "has-property-descriptors@^1.0.0": "https://registry.npmmirror.com/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz", "has-symbols@^1.0.3": "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.1.0.tgz", "has-symbols@^1.1.0": "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.1.0.tgz", "has-tostringtag@^1.0.2": "https://registry.npmmirror.com/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "hasown@^2.0.2": "https://registry.npmmirror.com/hasown/-/hasown-2.0.2.tgz", "he@^1.2.0": "https://registry.npmmirror.com/he/-/he-1.2.0.tgz", "hosted-git-info@^4.1.0": "https://registry.npmmirror.com/hosted-git-info/-/hosted-git-info-4.1.0.tgz", "http-cache-semantics@^4.0.0": "https://registry.npmmirror.com/http-cache-semantics/-/http-cache-semantics-4.2.0.tgz", "http-proxy-agent@^5.0.0": "https://registry.npmmirror.com/http-proxy-agent/-/http-proxy-agent-5.0.0.tgz", "http2-wrapper@^1.0.0-beta.5.2": "https://registry.npmmirror.com/http2-wrapper/-/http2-wrapper-1.0.3.tgz", "https-proxy-agent@^5.0.1": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "iconv-corefoundation@^1.1.7": "https://registry.npmmirror.com/iconv-corefoundation/-/iconv-corefoundation-1.1.7.tgz", "iconv-lite@^0.6.2": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.6.3.tgz", "ieee754@^1.1.13": "https://registry.npmmirror.com/ieee754/-/ieee754-1.2.1.tgz", "inflight@^1.0.4": "https://registry.npmmirror.com/inflight/-/inflight-1.0.6.tgz", "inherits@2": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "is-ci@^3.0.0": "https://registry.npmmirror.com/is-ci/-/is-ci-3.0.1.tgz", "is-fullwidth-code-point@^3.0.0": "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "isbinaryfile@^4.0.8": "https://registry.npmmirror.com/isbinaryfile/-/isbinaryfile-4.0.10.tgz", "isbinaryfile@^5.0.0": "https://registry.npmmirror.com/isbinaryfile/-/isbinaryfile-5.0.4.tgz", "isexe@^2.0.0": "https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz", "jackspeak@^3.1.2": "https://registry.npmmirror.com/jackspeak/-/jackspeak-3.4.3.tgz", "jake@^10.8.5": "https://registry.npmmirror.com/jake/-/jake-10.9.2.tgz", "joi@^17.11.0": "https://registry.npmmirror.com/joi/-/joi-17.13.3.tgz", "js-yaml@^4.1.0": "https://registry.npmmirror.com/js-yaml/-/js-yaml-4.1.0.tgz", "json-buffer@3.0.1": "https://registry.npmmirror.com/json-buffer/-/json-buffer-3.0.1.tgz", "json-schema-traverse@^0.4.1": "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "json-stringify-safe@^5.0.1": "https://registry.npmmirror.com/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "json5@^2.2.0": "https://registry.npmmirror.com/json5/-/json5-2.2.3.tgz", "jsonfile@^4.0.0": "https://registry.npmmirror.com/jsonfile/-/jsonfile-4.0.0.tgz", "jsonfile@^6.0.1": "https://registry.npmmirror.com/jsonfile/-/jsonfile-6.1.0.tgz", "keyv@^4.0.0": "https://registry.npmmirror.com/keyv/-/keyv-4.5.4.tgz", "lazy-val@^1.0.4": "https://registry.npmmirror.com/lazy-val/-/lazy-val-1.0.5.tgz", "lazy-val@^1.0.5": "https://registry.npmmirror.com/lazy-val/-/lazy-val-1.0.5.tgz", "lodash@^4.17.15": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.21": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "lowercase-keys@^2.0.0": "https://registry.npmmirror.com/lowercase-keys/-/lowercase-keys-2.0.0.tgz", "lru-cache@^10.2.0": "https://registry.npmmirror.com/lru-cache/-/lru-cache-10.4.3.tgz", "lru-cache@^6.0.0": "https://registry.npmmirror.com/lru-cache/-/lru-cache-6.0.0.tgz", "magic-string@^0.30.17": "https://registry.npmmirror.com/magic-string/-/magic-string-0.30.17.tgz", "matcher@^3.0.0": "https://registry.npmmirror.com/matcher/-/matcher-3.0.0.tgz", "math-intrinsics@^1.1.0": "https://registry.npmmirror.com/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "mime-db@1.52.0": "https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz", "mime-types@^2.1.12": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "mime@^2.5.2": "https://registry.npmmirror.com/mime/-/mime-2.6.0.tgz", "mimic-response@^1.0.0": "https://registry.npmmirror.com/mimic-response/-/mimic-response-1.0.1.tgz", "mimic-response@^3.1.0": "https://registry.npmmirror.com/mimic-response/-/mimic-response-3.1.0.tgz", "minimatch@^3.0.4": "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^3.1.1": "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^3.1.2": "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^5.0.1": "https://registry.npmmirror.com/minimatch/-/minimatch-5.1.6.tgz", "minimatch@^5.1.1": "https://registry.npmmirror.com/minimatch/-/minimatch-5.1.6.tgz", "minimatch@^9.0.3": "https://registry.npmmirror.com/minimatch/-/minimatch-9.0.5.tgz", "minimatch@^9.0.4": "https://registry.npmmirror.com/minimatch/-/minimatch-9.0.5.tgz", "minimist@^1.2.6": "https://registry.npmmirror.com/minimist/-/minimist-1.2.8.tgz", "minimist@^1.2.8": "https://registry.npmmirror.com/minimist/-/minimist-1.2.8.tgz", "minipass@^3.0.0": "https://registry.npmmirror.com/minipass/-/minipass-3.3.6.tgz", "minipass@^5.0.0": "https://registry.npmmirror.com/minipass/-/minipass-5.0.0.tgz", "minipass@^5.0.0 || ^6.0.2 || ^7.0.0": "https://registry.npmmirror.com/minipass/-/minipass-7.1.2.tgz", "minipass@^7.1.2": "https://registry.npmmirror.com/minipass/-/minipass-7.1.2.tgz", "minizlib@^2.1.1": "https://registry.npmmirror.com/minizlib/-/minizlib-2.1.2.tgz", "mkdirp@^1.0.3": "https://registry.npmmirror.com/mkdirp/-/mkdirp-1.0.4.tgz", "ms@^2.1.3": "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz", "muggle-string@^0.3.1": "https://registry.npmmirror.com/muggle-string/-/muggle-string-0.3.1.tgz", "nanoid@^3.3.11": "https://registry.npmmirror.com/nanoid/-/nanoid-3.3.11.tgz", "node-addon-api@^1.6.3": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.7.2.tgz", "normalize-url@^6.0.1": "https://registry.npmmirror.com/normalize-url/-/normalize-url-6.1.0.tgz", "object-keys@^1.1.1": "https://registry.npmmirror.com/object-keys/-/object-keys-1.1.1.tgz", "once@^1.3.0": "https://registry.npmmirror.com/once/-/once-1.4.0.tgz", "once@^1.3.1": "https://registry.npmmirror.com/once/-/once-1.4.0.tgz", "once@^1.4.0": "https://registry.npmmirror.com/once/-/once-1.4.0.tgz", "p-cancelable@^2.0.0": "https://registry.npmmirror.com/p-cancelable/-/p-cancelable-2.1.1.tgz", "package-json-from-dist@^1.0.0": "https://registry.npmmirror.com/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz", "path-browserify@^1.0.1": "https://registry.npmmirror.com/path-browserify/-/path-browserify-1.0.1.tgz", "path-is-absolute@^1.0.0": "https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "path-key@^3.1.0": "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz", "path-scurry@^1.11.1": "https://registry.npmmirror.com/path-scurry/-/path-scurry-1.11.1.tgz", "pend@~1.2.0": "https://registry.npmmirror.com/pend/-/pend-1.2.0.tgz", "picocolors@^1.1.1": "https://registry.npmmirror.com/picocolors/-/picocolors-1.1.1.tgz", "plist@^3.0.4": "https://registry.npmmirror.com/plist/-/plist-3.1.0.tgz", "plist@^3.0.5": "https://registry.npmmirror.com/plist/-/plist-3.1.0.tgz", "postcss@^8.4.43": "https://registry.npmmirror.com/postcss/-/postcss-8.5.6.tgz", "postcss@^8.5.6": "https://registry.npmmirror.com/postcss/-/postcss-8.5.6.tgz", "progress@^2.0.3": "https://registry.npmmirror.com/progress/-/progress-2.0.3.tgz", "promise-retry@^2.0.1": "https://registry.npmmirror.com/promise-retry/-/promise-retry-2.0.1.tgz", "proxy-from-env@^1.1.0": "https://registry.npmmirror.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "pump@^3.0.0": "https://registry.npmmirror.com/pump/-/pump-3.0.2.tgz", "punycode@^2.1.0": "https://registry.npmmirror.com/punycode/-/punycode-2.3.1.tgz", "quick-lru@^5.1.1": "https://registry.npmmirror.com/quick-lru/-/quick-lru-5.1.1.tgz", "read-config-file@6.3.2": "https://registry.npmmirror.com/read-config-file/-/read-config-file-6.3.2.tgz", "require-directory@^2.1.1": "https://registry.npmmirror.com/require-directory/-/require-directory-2.1.1.tgz", "resolve-alpn@^1.0.0": "https://registry.npmmirror.com/resolve-alpn/-/resolve-alpn-1.2.1.tgz", "responselike@^2.0.0": "https://registry.npmmirror.com/responselike/-/responselike-2.0.1.tgz", "retry@^0.12.0": "https://registry.npmmirror.com/retry/-/retry-0.12.0.tgz", "roarr@^2.15.3": "https://registry.npmmirror.com/roarr/-/roarr-2.15.4.tgz", "rollup@^4.20.0": "https://registry.npmmirror.com/rollup/-/rollup-4.44.2.tgz", "rxjs@^7.8.1": "https://registry.npmmirror.com/rxjs/-/rxjs-7.8.2.tgz", "safer-buffer@>= 2.1.2 < 3.0.0": "https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz", "sanitize-filename@^1.6.3": "https://registry.npmmirror.com/sanitize-filename/-/sanitize-filename-1.6.3.tgz", "sax@^1.2.4": "https://registry.npmmirror.com/sax/-/sax-1.4.1.tgz", "semver-compare@^1.0.0": "https://registry.npmmirror.com/semver-compare/-/semver-compare-1.0.0.tgz", "semver@^6.2.0": "https://registry.npmmirror.com/semver/-/semver-6.3.1.tgz", "semver@^7.3.2": "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz", "semver@^7.3.8": "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz", "semver@^7.5.3": "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz", "semver@^7.5.4": "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz", "serialize-error@^7.0.1": "https://registry.npmmirror.com/serialize-error/-/serialize-error-7.0.1.tgz", "shebang-command@^2.0.0": "https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz", "shebang-regex@^3.0.0": "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz", "shell-quote@^1.8.1": "https://registry.npmmirror.com/shell-quote/-/shell-quote-1.8.3.tgz", "signal-exit@^4.0.1": "https://registry.npmmirror.com/signal-exit/-/signal-exit-4.1.0.tgz", "simple-update-notifier@2.0.0": "https://registry.npmmirror.com/simple-update-notifier/-/simple-update-notifier-2.0.0.tgz", "slice-ansi@^3.0.0": "https://registry.npmmirror.com/slice-ansi/-/slice-ansi-3.0.0.tgz", "smart-buffer@^4.0.2": "https://registry.npmmirror.com/smart-buffer/-/smart-buffer-4.2.0.tgz", "source-map-js@^1.2.1": "https://registry.npmmirror.com/source-map-js/-/source-map-js-1.2.1.tgz", "source-map-support@^0.5.19": "https://registry.npmmirror.com/source-map-support/-/source-map-support-0.5.21.tgz", "source-map@^0.6.0": "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz", "spawn-command@0.0.2": "https://registry.npmmirror.com/spawn-command/-/spawn-command-0.0.2.tgz", "sprintf-js@^1.1.2": "https://registry.npmmirror.com/sprintf-js/-/sprintf-js-1.1.3.tgz", "stat-mode@^1.0.0": "https://registry.npmmirror.com/stat-mode/-/stat-mode-1.0.0.tgz", "string-width-cjs@npm:string-width@^4.2.0": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "string-width@^4.1.0": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.0": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.3": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "string-width@^5.0.1": "https://registry.npmmirror.com/string-width/-/string-width-5.1.2.tgz", "string-width@^5.1.2": "https://registry.npmmirror.com/string-width/-/string-width-5.1.2.tgz", "strip-ansi-cjs@npm:strip-ansi@^6.0.1": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^6.0.0": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^6.0.1": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^7.0.1": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-7.1.0.tgz", "sumchecker@^3.0.1": "https://registry.npmmirror.com/sumchecker/-/sumchecker-3.0.1.tgz", "supports-color@^7.1.0": "https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz", "supports-color@^8.1.1": "https://registry.npmmirror.com/supports-color/-/supports-color-8.1.1.tgz", "tar@^6.1.12": "https://registry.npmmirror.com/tar/-/tar-6.2.1.tgz", "temp-file@^3.4.0": "https://registry.npmmirror.com/temp-file/-/temp-file-3.4.0.tgz", "tmp-promise@^3.0.2": "https://registry.npmmirror.com/tmp-promise/-/tmp-promise-3.0.3.tgz", "tmp@^0.2.0": "https://registry.npmmirror.com/tmp/-/tmp-0.2.3.tgz", "tree-kill@^1.2.2": "https://registry.npmmirror.com/tree-kill/-/tree-kill-1.2.2.tgz", "truncate-utf8-bytes@^1.0.0": "https://registry.npmmirror.com/truncate-utf8-bytes/-/truncate-utf8-bytes-1.0.2.tgz", "tslib@^2.1.0": "https://registry.npmmirror.com/tslib/-/tslib-2.8.1.tgz", "type-fest@^0.13.1": "https://registry.npmmirror.com/type-fest/-/type-fest-0.13.1.tgz", "typescript@^5.3.0": "https://registry.npmmirror.com/typescript/-/typescript-5.8.3.tgz", "typescript@^5.3.3": "https://registry.npmmirror.com/typescript/-/typescript-5.8.3.tgz", "undici-types@~5.26.4": "https://registry.npmmirror.com/undici-types/-/undici-types-5.26.5.tgz", "undici-types@~6.21.0": "https://registry.npmmirror.com/undici-types/-/undici-types-6.21.0.tgz", "universalify@^0.1.0": "https://registry.npmmirror.com/universalify/-/universalify-0.1.2.tgz", "universalify@^2.0.0": "https://registry.npmmirror.com/universalify/-/universalify-2.0.1.tgz", "uri-js@^4.2.2": "https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz", "utf8-byte-length@^1.0.1": "https://registry.npmmirror.com/utf8-byte-length/-/utf8-byte-length-1.0.5.tgz", "verror@^1.10.0": "https://registry.npmmirror.com/verror/-/verror-1.10.1.tgz", "vite-plugin-electron-renderer@^0.14.0": "https://registry.npmmirror.com/vite-plugin-electron-renderer/-/vite-plugin-electron-renderer-0.14.6.tgz", "vite-plugin-electron@^0.28.0": "https://registry.npmmirror.com/vite-plugin-electron/-/vite-plugin-electron-0.28.8.tgz", "vite@^5.0.0": "https://registry.npmmirror.com/vite/-/vite-5.4.19.tgz", "vue-router@4": "https://registry.npmmirror.com/vue-router/-/vue-router-4.5.1.tgz#47bffe2d3a5479d2886a9a244547a853aa0abf69", "vue-template-compiler@^2.7.14": "https://registry.npmmirror.com/vue-template-compiler/-/vue-template-compiler-2.7.16.tgz", "vue-tsc@^1.8.0": "https://registry.npmmirror.com/vue-tsc/-/vue-tsc-1.8.27.tgz", "vue@^3.4.0": "https://registry.npmmirror.com/vue/-/vue-3.5.17.tgz", "wait-on@^7.2.0": "https://registry.npmmirror.com/wait-on/-/wait-on-7.2.0.tgz", "which@^2.0.1": "https://registry.npmmirror.com/which/-/which-2.0.2.tgz", "wrap-ansi-cjs@npm:wrap-ansi@^7.0.0": "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "wrap-ansi@^7.0.0": "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "wrap-ansi@^8.1.0": "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-8.1.0.tgz", "wrappy@1": "https://registry.npmmirror.com/wrappy/-/wrappy-1.0.2.tgz", "xmlbuilder@>=11.0.1": "https://registry.npmmirror.com/xmlbuilder/-/xmlbuilder-15.1.1.tgz", "xmlbuilder@^15.1.1": "https://registry.npmmirror.com/xmlbuilder/-/xmlbuilder-15.1.1.tgz", "y18n@^5.0.5": "https://registry.npmmirror.com/y18n/-/y18n-5.0.8.tgz", "yallist@^4.0.0": "https://registry.npmmirror.com/yallist/-/yallist-4.0.0.tgz", "yargs-parser@^21.1.1": "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-21.1.1.tgz", "yargs@^17.6.2": "https://registry.npmmirror.com/yargs/-/yargs-17.7.2.tgz", "yargs@^17.7.2": "https://registry.npmmirror.com/yargs/-/yargs-17.7.2.tgz", "yauzl@^2.10.0": "https://registry.npmmirror.com/yauzl/-/yauzl-2.10.0.tgz"}, "files": [], "artifacts": {"electron@28.3.3": ["dist", "dist/Electron.app", "dist/Electron.app/Contents", "dist/Electron.app/Contents/Frameworks", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Electron Framework", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Helpers", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Libraries", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Resources", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Electron Framework", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Helpers", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Helpers/chrome_crashpad_handler", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Libraries", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Libraries/libEGL.dylib", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Libraries/libGLESv2.dylib", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Libraries/libffmpeg.dylib", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Libraries/libvk_swiftshader.dylib", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Libraries/vk_swiftshader_icd.json", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/Info.plist", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/MainMenu.nib", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/af.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/af.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/am.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/am.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ar.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ar.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/bg.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/bg.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/bn.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/bn.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ca.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ca.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/chrome_100_percent.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/chrome_200_percent.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/cs.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/cs.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/da.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/da.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/de.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/de.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/el.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/el.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/en.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/en.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/en_GB.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/en_GB.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/es.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/es.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/es_419.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/es_419.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/et.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/et.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/fa.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/fa.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/fi.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/fi.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/fil.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/fil.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/fr.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/fr.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/gu.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/gu.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/he.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/he.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/hi.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/hi.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/hr.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/hr.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/hu.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/hu.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/icudtl.dat", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/id.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/id.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/it.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/it.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ja.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ja.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/kn.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/kn.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ko.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ko.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/lt.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/lt.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/lv.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/lv.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ml.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ml.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/mr.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/mr.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ms.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ms.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/nb.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/nb.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/nl.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/nl.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/pl.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/pl.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/pt_BR.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/pt_BR.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/pt_PT.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/pt_PT.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/resources.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ro.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ro.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ru.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ru.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/sk.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/sk.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/sl.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/sl.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/sr.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/sr.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/sv.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/sv.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/sw.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/sw.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ta.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ta.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/te.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/te.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/th.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/th.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/tr.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/tr.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/uk.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/uk.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ur.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/ur.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/v8_context_snapshot.arm64.bin", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/vi.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/vi.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/zh_CN.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/zh_CN.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/zh_TW.lproj", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/zh_TW.lproj/locale.pak", "dist/Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/Current", "dist/Electron.app/Contents/Frameworks/Electron Helper (GPU).app", "dist/Electron.app/Contents/Frameworks/Electron Helper (GPU).app/Contents", "dist/Electron.app/Contents/Frameworks/Electron Helper (GPU).app/Contents/Info.plist", "dist/Electron.app/Contents/Frameworks/Electron Helper (GPU).app/Contents/MacOS", "dist/Electron.app/Contents/Frameworks/Electron Helper (GPU).app/Contents/MacOS/Electron Helper (GPU)", "dist/Electron.app/Contents/Frameworks/Electron Helper (GPU).app/Contents/PkgInfo", "dist/Electron.app/Contents/Frameworks/Electron Helper (Plugin).app", "dist/Electron.app/Contents/Frameworks/Electron Helper (Plugin).app/Contents", "dist/Electron.app/Contents/Frameworks/Electron Helper (Plugin).app/Contents/Info.plist", "dist/Electron.app/Contents/Frameworks/Electron Helper (Plugin).app/Contents/MacOS", "dist/Electron.app/Contents/Frameworks/Electron Helper (Plugin).app/Contents/MacOS/Electron Helper (Plugin)", "dist/Electron.app/Contents/Frameworks/Electron Helper (Plugin).app/Contents/PkgInfo", "dist/Electron.app/Contents/Frameworks/Electron Helper (Renderer).app", "dist/Electron.app/Contents/Frameworks/Electron Helper (Renderer).app/Contents", "dist/Electron.app/Contents/Frameworks/Electron Helper (Renderer).app/Contents/Info.plist", "dist/Electron.app/Contents/Frameworks/Electron Helper (Renderer).app/Contents/MacOS", "dist/Electron.app/Contents/Frameworks/Electron Helper (Renderer).app/Contents/MacOS/Electron Helper (Renderer)", "dist/Electron.app/Contents/Frameworks/Electron Helper (Renderer).app/Contents/PkgInfo", "dist/Electron.app/Contents/Frameworks/Electron Helper.app", "dist/Electron.app/Contents/Frameworks/Electron Helper.app/Contents", "dist/Electron.app/Contents/Frameworks/Electron Helper.app/Contents/Info.plist", "dist/Electron.app/Contents/Frameworks/Electron Helper.app/Contents/MacOS", "dist/Electron.app/Contents/Frameworks/Electron Helper.app/Contents/MacOS/Electron Helper", "dist/Electron.app/Contents/Frameworks/Electron Helper.app/Contents/PkgInfo", "dist/Electron.app/Contents/Frameworks/Mantle.framework", "dist/Electron.app/Contents/Frameworks/Mantle.framework/Mantle", "dist/Electron.app/Contents/Frameworks/Mantle.framework/Resources", "dist/Electron.app/Contents/Frameworks/Mantle.framework/Versions", "dist/Electron.app/Contents/Frameworks/Mantle.framework/Versions/A", "dist/Electron.app/Contents/Frameworks/Mantle.framework/Versions/A/Mantle", "dist/Electron.app/Contents/Frameworks/Mantle.framework/Versions/A/Resources", "dist/Electron.app/Contents/Frameworks/Mantle.framework/Versions/A/Resources/Info.plist", "dist/Electron.app/Contents/Frameworks/Mantle.framework/Versions/Current", "dist/Electron.app/Contents/Frameworks/ReactiveObjC.framework", "dist/Electron.app/Contents/Frameworks/ReactiveObjC.framework/ReactiveObjC", "dist/Electron.app/Contents/Frameworks/ReactiveObjC.framework/Resources", "dist/Electron.app/Contents/Frameworks/ReactiveObjC.framework/Versions", "dist/Electron.app/Contents/Frameworks/ReactiveObjC.framework/Versions/A", "dist/Electron.app/Contents/Frameworks/ReactiveObjC.framework/Versions/A/ReactiveObjC", "dist/Electron.app/Contents/Frameworks/ReactiveObjC.framework/Versions/A/Resources", "dist/Electron.app/Contents/Frameworks/ReactiveObjC.framework/Versions/A/Resources/Info.plist", "dist/Electron.app/Contents/Frameworks/ReactiveObjC.framework/Versions/Current", "dist/Electron.app/Contents/Frameworks/Squirrel.framework", "dist/Electron.app/Contents/Frameworks/Squirrel.framework/Resources", "dist/Electron.app/Contents/Frameworks/Squirrel.framework/Squirrel", "dist/Electron.app/Contents/Frameworks/Squirrel.framework/Versions", "dist/Electron.app/Contents/Frameworks/Squirrel.framework/Versions/A", "dist/Electron.app/Contents/Frameworks/Squirrel.framework/Versions/A/Resources", "dist/Electron.app/Contents/Frameworks/Squirrel.framework/Versions/A/Resources/Info.plist", "dist/Electron.app/Contents/Frameworks/Squirrel.framework/Versions/A/Resources/ShipIt", "dist/Electron.app/Contents/Frameworks/Squirrel.framework/Versions/A/Squirrel", "dist/Electron.app/Contents/Frameworks/Squirrel.framework/Versions/Current", "dist/Electron.app/Contents/Info.plist", "dist/Electron.app/Contents/MacOS", "dist/Electron.app/Contents/MacOS/Electron", "dist/Electron.app/Contents/PkgInfo", "dist/Electron.app/Contents/Resources", "dist/Electron.app/Contents/Resources/af.lproj", "dist/Electron.app/Contents/Resources/am.lproj", "dist/Electron.app/Contents/Resources/ar.lproj", "dist/Electron.app/Contents/Resources/bg.lproj", "dist/Electron.app/Contents/Resources/bn.lproj", "dist/Electron.app/Contents/Resources/ca.lproj", "dist/Electron.app/Contents/Resources/cs.lproj", "dist/Electron.app/Contents/Resources/da.lproj", "dist/Electron.app/Contents/Resources/de.lproj", "dist/Electron.app/Contents/Resources/default_app.asar", "dist/Electron.app/Contents/Resources/el.lproj", "dist/Electron.app/Contents/Resources/electron.icns", "dist/Electron.app/Contents/Resources/en.lproj", "dist/Electron.app/Contents/Resources/en_GB.lproj", "dist/Electron.app/Contents/Resources/es.lproj", "dist/Electron.app/Contents/Resources/es_419.lproj", "dist/Electron.app/Contents/Resources/et.lproj", "dist/Electron.app/Contents/Resources/fa.lproj", "dist/Electron.app/Contents/Resources/fi.lproj", "dist/Electron.app/Contents/Resources/fil.lproj", "dist/Electron.app/Contents/Resources/fr.lproj", "dist/Electron.app/Contents/Resources/gu.lproj", "dist/Electron.app/Contents/Resources/he.lproj", "dist/Electron.app/Contents/Resources/hi.lproj", "dist/Electron.app/Contents/Resources/hr.lproj", "dist/Electron.app/Contents/Resources/hu.lproj", "dist/Electron.app/Contents/Resources/id.lproj", "dist/Electron.app/Contents/Resources/it.lproj", "dist/Electron.app/Contents/Resources/ja.lproj", "dist/Electron.app/Contents/Resources/kn.lproj", "dist/Electron.app/Contents/Resources/ko.lproj", "dist/Electron.app/Contents/Resources/lt.lproj", "dist/Electron.app/Contents/Resources/lv.lproj", "dist/Electron.app/Contents/Resources/ml.lproj", "dist/Electron.app/Contents/Resources/mr.lproj", "dist/Electron.app/Contents/Resources/ms.lproj", "dist/Electron.app/Contents/Resources/nb.lproj", "dist/Electron.app/Contents/Resources/nl.lproj", "dist/Electron.app/Contents/Resources/pl.lproj", "dist/Electron.app/Contents/Resources/pt_BR.lproj", "dist/Electron.app/Contents/Resources/pt_PT.lproj", "dist/Electron.app/Contents/Resources/ro.lproj", "dist/Electron.app/Contents/Resources/ru.lproj", "dist/Electron.app/Contents/Resources/sk.lproj", "dist/Electron.app/Contents/Resources/sl.lproj", "dist/Electron.app/Contents/Resources/sr.lproj", "dist/Electron.app/Contents/Resources/sv.lproj", "dist/Electron.app/Contents/Resources/sw.lproj", "dist/Electron.app/Contents/Resources/ta.lproj", "dist/Electron.app/Contents/Resources/te.lproj", "dist/Electron.app/Contents/Resources/th.lproj", "dist/Electron.app/Contents/Resources/tr.lproj", "dist/Electron.app/Contents/Resources/uk.lproj", "dist/Electron.app/Contents/Resources/ur.lproj", "dist/Electron.app/Contents/Resources/vi.lproj", "dist/Electron.app/Contents/Resources/zh_CN.lproj", "dist/Electron.app/Contents/Resources/zh_TW.lproj", "dist/LICENSE", "dist/LICENSES.chromium.html", "dist/version", "path.txt"]}}