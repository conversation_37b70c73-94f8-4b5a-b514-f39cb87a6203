{"name": "augment_activator", "version": "1.0.0", "description": "Vue3 + Electron应用程序", "main": "dist-electron/main.js", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build && electron-builder", "preview": "vite preview", "electron": "wait-on tcp:5173 && cross-env IS_DEV=true electron .", "electron:pack": "vue-tsc --noEmit && vite build && electron-builder", "electron:dev": "concurrently -k \"cross-env BROWSER=none npm run dev\" \"npm run electron\"", "electron:preview": "npm run build && electron .", "pack": "electron-builder --dir", "dist": "npm run build"}, "keywords": ["electron"], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^5.0.0", "concurrently": "^8.2.0", "cross-env": "^7.0.3", "electron": "^28.0.0", "electron-builder": "^24.9.0", "typescript": "^5.3.0", "vite": "^5.0.0", "vite-plugin-electron": "^0.28.0", "vite-plugin-electron-renderer": "^0.14.0", "vue-tsc": "^1.8.0", "wait-on": "^7.2.0"}, "dependencies": {"vue": "^3.4.0", "vue-router": "4"}, "build": {"appId": "com.xunhewenhua.augment-activator", "productName": "Augment账号分发系统", "directories": {"buildResources": "assets", "output": "release/${version}"}, "files": ["dist-electron", "dist"], "mac": {"artifactName": "${productName}_${version}.${ext}", "target": [{"target": "dmg", "arch": ["arm64", "x64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "artifactName": "${productName}_${version}.${ext}"}, "nsis": {"oneClick": false, "perMachine": false, "allowToChangeInstallationDirectory": true, "deleteAppDataOnUninstall": false}}}